"use client"

import { useState } from "react"
import {
  Heart,
  MessageCircle,
  Star,
  TrendingUp,
  Search,
  User,
  Settings,
  LogOut,
  Zap,
  Code,
  Briefcase,
  GraduationCap,
  Sparkles,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import Link from "next/link"

// 科技感分类配置
const categories = [
  {
    id: "tools",
    name: "在线工具",
    icon: Zap,
    gradient: "from-cyan-400 to-blue-500",
    bgGradient: "from-cyan-50 to-blue-50",
    description: "高效便捷工具",
  },
  {
    id: "office",
    name: "办公",
    icon: Briefcase,
    gradient: "from-emerald-400 to-teal-500",
    bgGradient: "from-emerald-50 to-teal-50",
    description: "办公效率提升",
  },
  {
    id: "k12",
    name: "K12",
    icon: GraduationCap,
    gradient: "from-purple-400 to-indigo-500",
    bgGradient: "from-purple-50 to-indigo-50",
    description: "教育学习资源",
  },
  {
    id: "fun",
    name: "有趣网站",
    icon: Sparkles,
    gradient: "from-orange-400 to-red-500",
    bgGradient: "from-orange-50 to-red-50",
    description: "创意趣味网站",
  },
]

const mockResources = [
  {
    id: 1,
    title: "PDF转换大师",
    description: "在线PDF格式转换工具，支持多种格式互转",
    category: "tools",
    thumbnail: "/pdf-converter-interface.png",
    likes: 1234,
    dislikes: 12,
    comments: 89,
    saves: 456,
    isHot: true,
    rating: 4.8,
    tags: ["PDF", "转换", "办公"],
  },
  {
    id: 2,
    title: "思维导图制作",
    description: "专业的在线思维导图制作工具",
    category: "office",
    thumbnail: "/mind-mapping-tool-interface.png",
    likes: 987,
    dislikes: 8,
    comments: 67,
    saves: 234,
    isHot: false,
    rating: 4.6,
    tags: ["思维导图", "协作", "可视化"],
  },
  {
    id: 3,
    title: "数学公式编辑器",
    description: "适合学生和老师的数学公式在线编辑工具",
    category: "k12",
    thumbnail: "/math-formula-editor.png",
    likes: 756,
    dislikes: 15,
    comments: 45,
    saves: 189,
    isHot: true,
    rating: 4.7,
    tags: ["数学", "公式", "教育"],
  },
  {
    id: 4,
    title: "创意绘画板",
    description: "有趣的在线绘画工具，释放你的创造力",
    category: "fun",
    thumbnail: "/creative-drawing-board.png",
    likes: 2341,
    dislikes: 23,
    comments: 156,
    saves: 678,
    isHot: true,
    rating: 4.9,
    tags: ["绘画", "创意", "艺术"],
  },
  {
    id: 5,
    title: "代码格式化工具",
    description: "支持多种编程语言的代码美化工具",
    category: "tools",
    thumbnail: "/code-formatter-interface.png",
    likes: 1567,
    dislikes: 19,
    comments: 234,
    saves: 445,
    isHot: false,
    rating: 4.5,
    tags: ["代码", "格式化", "开发"],
  },
  {
    id: 6,
    title: "PPT模板库",
    description: "海量精美PPT模板，提升演示效果",
    category: "office",
    thumbnail: "/powerpoint-template-library.png",
    likes: 3456,
    dislikes: 34,
    comments: 567,
    saves: 1234,
    isHot: true,
    rating: 4.8,
    tags: ["PPT", "模板", "演示"],
  },
]

export default function HomePage() {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [activeCategory, setActiveCategory] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [sidebarExpanded, setSidebarExpanded] = useState(true)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)

  const filteredResources = mockResources.filter((resource) => {
    const matchesCategory = activeCategory === "all" || resource.category === activeCategory
    const matchesSearch =
      resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesCategory && matchesSearch
  })

  const handleInteraction = (action: string) => {
    if (!isLoggedIn) {
      alert("登录后可操作")
      return
    }
    console.log(`${action} clicked`)
  }

  const getCategoryConfig = (categoryId: string) => {
    return categories.find((cat) => cat.id === categoryId) || categories[0]
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 科技感背景装饰 - 缩小 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-32 -right-32 w-64 h-64 bg-gradient-to-br from-blue-400/15 to-purple-600/15 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-gradient-to-tr from-cyan-400/15 to-blue-600/15 rounded-full blur-3xl"></div>
      </div>

      {/* 移动端遮罩 */}
      {mobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/40 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setMobileSidebarOpen(false)}
        />
      )}

      {/* 左侧导航栏 - 紧凑版 */}
      <aside
        className={`fixed left-0 top-0 h-full bg-white/90 backdrop-blur-xl border-r border-gray-200/50 z-50 transition-all duration-300 ease-in-out ${
          sidebarExpanded ? "w-64" : "w-16"
        } ${mobileSidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}`}
      >
        <div className="flex flex-col h-full">
          {/* Logo区域 - 紧凑 */}
          <div className="p-3 border-b border-gray-200/50">
            <div className="flex items-center justify-between">
              <Link href="/" className="flex items-center space-x-2 group">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300">
                  <Code className="w-4 h-4 text-white" />
                </div>
                {sidebarExpanded && (
                  <div className="overflow-hidden">
                    <span className="text-lg font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent whitespace-nowrap">
                      FORMAT123
                    </span>
                  </div>
                )}
              </Link>

              {/* 展开/收起按钮 - 桌面端 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarExpanded(!sidebarExpanded)}
                className="hidden lg:flex w-6 h-6 rounded-lg hover:bg-gray-100/80 transition-all duration-300"
              >
                {sidebarExpanded ? <ChevronLeft className="w-3 h-3" /> : <ChevronRight className="w-3 h-3" />}
              </Button>

              {/* 关闭按钮 - 移动端 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileSidebarOpen(false)}
                className="lg:hidden w-6 h-6 rounded-lg hover:bg-gray-100/80"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>

          {/* 导航菜单 - 紧凑 */}
          <nav className="flex-1 p-2 space-y-1">
            {/* 全部资源 */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={activeCategory === "all" ? "default" : "ghost"}
                    onClick={() => setActiveCategory("all")}
                    className={`w-full justify-start h-10 rounded-lg transition-all duration-300 ${
                      activeCategory === "all"
                        ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md hover:shadow-lg"
                        : "hover:bg-gray-100/80 text-gray-700"
                    } ${!sidebarExpanded ? "px-0 justify-center" : "px-3"}`}
                  >
                    <div className="flex items-center space-x-2">
                      <div
                        className={`w-6 h-6 rounded-lg flex items-center justify-center transition-all duration-300 ${
                          activeCategory === "all" ? "bg-white/20" : "bg-gradient-to-br from-gray-100 to-gray-200"
                        }`}
                      >
                        <Sparkles className="w-3 h-3" />
                      </div>
                      {sidebarExpanded && (
                        <div className="overflow-hidden">
                          <div className="font-medium text-sm whitespace-nowrap">全部资源</div>
                          <div className="text-xs opacity-80 whitespace-nowrap">探索所有内容</div>
                        </div>
                      )}
                    </div>
                  </Button>
                </TooltipTrigger>
                {!sidebarExpanded && <TooltipContent side="right">全部资源</TooltipContent>}
              </Tooltip>
            </TooltipProvider>

            {/* 分类菜单 */}
            {categories.map((category) => {
              const Icon = category.icon
              const isActive = activeCategory === category.id
              return (
                <TooltipProvider key={category.id}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        onClick={() => setActiveCategory(category.id)}
                        className={`w-full justify-start h-10 rounded-lg transition-all duration-300 ${
                          isActive
                            ? `bg-gradient-to-r ${category.gradient} text-white shadow-md hover:shadow-lg`
                            : "hover:bg-gray-100/80 text-gray-700"
                        } ${!sidebarExpanded ? "px-0 justify-center" : "px-3"}`}
                      >
                        <div className="flex items-center space-x-2">
                          <div
                            className={`w-6 h-6 rounded-lg flex items-center justify-center transition-all duration-300 ${
                              isActive ? "bg-white/20" : `bg-gradient-to-br ${category.bgGradient}`
                            }`}
                          >
                            <Icon className="w-3 h-3" />
                          </div>
                          {sidebarExpanded && (
                            <div className="overflow-hidden">
                              <div className="font-medium text-sm whitespace-nowrap">{category.name}</div>
                              <div className="text-xs opacity-80 whitespace-nowrap">{category.description}</div>
                            </div>
                          )}
                        </div>
                      </Button>
                    </TooltipTrigger>
                    {!sidebarExpanded && <TooltipContent side="right">{category.name}</TooltipContent>}
                  </Tooltip>
                </TooltipProvider>
              )
            })}
          </nav>

          {/* 底部用户区域 - 紧凑 */}
          <div className="p-2 border-t border-gray-200/50">
            {!isLoggedIn ? (
              <div className={`space-y-2 ${!sidebarExpanded ? "flex flex-col items-center" : ""}`}>
                {sidebarExpanded ? (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => setIsLoggedIn(true)}
                      className="w-full h-8 text-xs rounded-lg border-gray-200/50 hover:bg-gray-50/80"
                    >
                      登录
                    </Button>
                    <Button
                      onClick={() => setIsLoggedIn(true)}
                      className="w-full h-8 text-xs rounded-lg bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-md"
                    >
                      注册
                    </Button>
                  </>
                ) : (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          onClick={() => setIsLoggedIn(true)}
                          className="w-8 h-8 rounded-lg bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-md"
                        >
                          <User className="w-3 h-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="right">登录/注册</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </div>
            ) : (
              <div className={`${!sidebarExpanded ? "flex justify-center" : ""}`}>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className={`rounded-lg hover:bg-gray-100/80 transition-all duration-300 ${
                        sidebarExpanded ? "w-full h-10 justify-start px-3" : "w-8 h-8 justify-center px-0"
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <Avatar className="w-6 h-6 border border-white/50 shadow-sm">
                          <AvatarImage src="/diverse-user-avatars.png" alt="用户头像" />
                          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs font-semibold">
                            用
                          </AvatarFallback>
                        </Avatar>
                        {sidebarExpanded && (
                          <div className="overflow-hidden text-left">
                            <div className="font-medium text-gray-900 text-sm whitespace-nowrap">张三</div>
                            <div className="text-xs text-gray-500 whitespace-nowrap"><EMAIL></div>
                          </div>
                        )}
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="w-48 bg-white/95 backdrop-blur-xl border-gray-200/50 rounded-lg"
                    align="end"
                    side={sidebarExpanded ? "top" : "right"}
                  >
                    <DropdownMenuItem asChild>
                      <Link href="/profile" className="flex items-center h-9 px-3 rounded-md">
                        <User className="mr-2 h-4 w-4" />
                        个人中心
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem className="h-9 px-3 rounded-md">
                      <Settings className="mr-2 h-4 w-4" />
                      设置
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setIsLoggedIn(false)} className="h-9 px-3 rounded-md">
                      <LogOut className="mr-2 h-4 w-4" />
                      退出登录
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>
        </div>
      </aside>

      {/* 主内容区域 */}
      <main
        className={`transition-all duration-300 ease-in-out ${sidebarExpanded ? "lg:ml-64" : "lg:ml-16"} min-h-screen`}
      >
        {/* 顶部工具栏 - 紧凑 */}
        <header className="sticky top-0 z-30 bg-white/90 backdrop-blur-xl border-b border-gray-200/50">
          <div className="px-4 lg:px-6">
            <div className="flex items-center justify-between h-14">
              {/* 移动端菜单按钮 */}
              <Button
                variant="ghost"
                onClick={() => setMobileSidebarOpen(true)}
                className="lg:hidden w-8 h-8 rounded-lg hover:bg-gray-100/80"
              >
                <Menu className="w-4 h-4" />
              </Button>

              {/* 搜索框 - 紧凑 */}
              <div className="flex-1 max-w-xl mx-auto">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="搜索资源..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 h-10 w-full bg-white/80 backdrop-blur-sm border-gray-200/50 rounded-lg focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50"
                  />
                </div>
              </div>

              {/* 右侧占位 */}
              <div className="w-8 lg:w-0"></div>
            </div>
          </div>
        </header>

        {/* 内容区域 - 紧凑 */}
        <div className="px-4 lg:px-6 py-4">
          {/* 页面标题 - 紧凑 */}
          <div className="mb-4">
            <h1 className="text-2xl font-bold mb-1 bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent">
              {activeCategory === "all"
                ? "探索全部资源"
                : categories.find((cat) => cat.id === activeCategory)?.name || "资源中心"}
            </h1>
            <p className="text-sm text-gray-600">
              {activeCategory === "all"
                ? "发现最优质的在线工具和资源"
                : categories.find((cat) => cat.id === activeCategory)?.description || ""}
            </p>
          </div>

          {/* 资源卡片网格 - 紧凑 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
            {filteredResources.map((resource) => {
              const categoryConfig = getCategoryConfig(resource.category)
              return (
                <TooltipProvider key={resource.id}>
                  <Card className="group relative overflow-hidden bg-white/90 backdrop-blur-sm border-0 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1 rounded-xl">
                    <CardContent className="p-0">
                      {/* 缩略图区域 - 紧凑 */}
                      <div className="relative overflow-hidden">
                        <div
                          className={`absolute inset-0 bg-gradient-to-br ${categoryConfig.bgGradient} opacity-20`}
                        ></div>
                        <img
                          src={resource.thumbnail || "/placeholder.svg"}
                          alt={resource.title}
                          className="w-full h-32 object-cover transition-transform duration-300 group-hover:scale-105"
                        />

                        {/* 渐变遮罩 */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

                        {/* Hot标签 - 紧凑 */}
                        {resource.isHot && (
                          <div className="absolute top-2 right-2">
                            <div className="flex items-center space-x-1 bg-gradient-to-r from-red-500 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium shadow-sm">
                              <TrendingUp className="w-2 h-2" />
                              <span>HOT</span>
                            </div>
                          </div>
                        )}

                        {/* 评分 - 紧凑 */}
                        <div className="absolute top-2 left-2">
                          <div className="flex items-center space-x-1 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium">
                            <Star className="w-2 h-2 text-yellow-500 fill-current" />
                            <span className="text-gray-700">{resource.rating}</span>
                          </div>
                        </div>

                        {/* 分类标识 - 紧凑 */}
                        <div className="absolute bottom-2 left-2">
                          <div
                            className={`flex items-center space-x-1 bg-gradient-to-r ${categoryConfig.gradient} text-white px-2 py-1 rounded-full text-xs font-medium shadow-sm`}
                          >
                            <categoryConfig.icon className="w-2 h-2" />
                            <span>{categoryConfig.name}</span>
                          </div>
                        </div>
                      </div>

                      {/* 内容区域 - 紧凑 */}
                      <div className="p-3">
                        <Link href={`/resource/${resource.id}`}>
                          <h3 className="font-semibold text-sm mb-2 text-gray-900 group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300 line-clamp-1">
                            {resource.title}
                          </h3>
                        </Link>

                        <p className="text-gray-600 text-xs mb-3 line-clamp-2 leading-relaxed">
                          {resource.description}
                        </p>

                        {/* 标签 - 紧凑 */}
                        <div className="flex flex-wrap gap-1 mb-3">
                          {resource.tags.slice(0, 3).map((tag, index) => (
                            <span
                              key={index}
                              className="px-2 py-0.5 bg-gray-100/80 text-gray-600 text-xs rounded-md font-medium"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {/* 交互按钮区域 - 紧凑 */}
                        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                          <div className="flex items-center space-x-3">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className={`h-6 px-1 rounded-md transition-all duration-300 ${
                                    !isLoggedIn ? "opacity-50" : "hover:bg-red-50 hover:text-red-500"
                                  }`}
                                  onClick={() => handleInteraction("like")}
                                >
                                  <Heart className="w-3 h-3 mr-1" />
                                  <span className="text-xs font-medium">{resource.likes}</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent className="bg-gray-900 text-white rounded-lg text-xs">
                                {!isLoggedIn ? "登录后可点赞" : "点赞"}
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 px-1 rounded-md hover:bg-blue-50 hover:text-blue-500"
                                  asChild
                                >
                                  <Link href={`/resource/${resource.id}`}>
                                    <MessageCircle className="w-3 h-3 mr-1" />
                                    <span className="text-xs font-medium">{resource.comments}</span>
                                  </Link>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent className="bg-gray-900 text-white rounded-lg text-xs">
                                查看评论
                              </TooltipContent>
                            </Tooltip>
                          </div>

                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className={`h-6 w-6 rounded-md transition-all duration-300 ${
                                  !isLoggedIn ? "opacity-50" : "hover:bg-yellow-50 hover:text-yellow-500"
                                }`}
                                onClick={() => handleInteraction("save")}
                              >
                                <Star className="w-3 h-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent className="bg-gray-900 text-white rounded-lg text-xs">
                              {!isLoggedIn ? "登录后可收藏" : "收藏"}
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TooltipProvider>
              )
            })}
          </div>

          {/* 空状态 - 紧凑 */}
          {filteredResources.length === 0 && (
            <div className="text-center py-16">
              <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
                <Search className="w-12 h-12 text-gray-400" />
              </div>
              <h3 className="text-xl font-bold mb-3 bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent">
                探索宝藏
              </h3>
              <p className="text-gray-500">暂未找到相关资源，试试其他关键词吧</p>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
