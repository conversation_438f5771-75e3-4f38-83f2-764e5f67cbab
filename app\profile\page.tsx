"use client"

import { useState } from "react"
import { ArrowLeft, Heart, MessageCircle, Settings, User, Bookmark, Clock, Zap, Menu } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import Link from "next/link"
import { useRouter } from "next/navigation"

const userData = {
  name: "张三",
  email: "<EMAIL>",
  avatar: "/user-profile-avatar.png",
  joinDate: "2024年1月",
  stats: {
    saves: 23,
    comments: 45,
    likes: 156,
  },
}

const savedResources = [
  {
    id: 1,
    title: "PDF转换大师",
    description: "在线PDF格式转换工具",
    category: "在线工具",
    thumbnail: "/pdf-converter-thumbnail.png",
    savedDate: "2024-01-15",
    gradient: "from-cyan-400 to-blue-500",
  },
  {
    id: 2,
    title: "思维导图制作",
    description: "专业的在线思维导图制作工具",
    category: "办公",
    thumbnail: "/placeholder-kj6u6.png",
    savedDate: "2024-01-12",
    gradient: "from-emerald-400 to-teal-500",
  },
  {
    id: 3,
    title: "代码格式化工具",
    description: "支持多种编程语言的代码美化工具",
    category: "在线工具",
    thumbnail: "/code-formatter-thumbnail.png",
    savedDate: "2024-01-10",
    gradient: "from-cyan-400 to-blue-500",
  },
]

const userComments = [
  {
    id: 1,
    resourceTitle: "PDF转换大师",
    resourceId: 1,
    content: "这个工具真的很好用，转换速度很快，质量也不错！界面设计也很简洁。",
    date: "2024-01-15",
    likes: 12,
  },
  {
    id: 2,
    resourceTitle: "思维导图制作",
    resourceId: 2,
    content: "界面简洁，操作简单，推荐给大家使用。特别是协作功能很实用。",
    date: "2024-01-12",
    likes: 8,
  },
  {
    id: 3,
    resourceTitle: "创意绘画板",
    resourceId: 4,
    content: "很有趣的工具，可以激发创造力。功能丰富，体验很好。",
    date: "2024-01-10",
    likes: 15,
  },
]

export default function ProfilePage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("saves")
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    name: userData.name,
    email: userData.email,
  })

  const handleSaveProfile = () => {
    console.log("保存用户信息:", editForm)
    setIsEditing(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 科技感背景装饰 - 缩小 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-32 -right-32 w-64 h-64 bg-gradient-to-br from-blue-400/15 to-purple-600/15 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-gradient-to-tr from-cyan-400/15 to-blue-600/15 rounded-full blur-3xl"></div>
      </div>

      {/* 导航栏 - 紧凑 */}
      <header className="relative bg-white/90 backdrop-blur-xl border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 lg:px-6">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="h-8 px-3 rounded-lg hover:bg-gray-100/80"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                返回
              </Button>
              {/* 移动端菜单按钮 */}
              <Button variant="ghost" className="lg:hidden w-8 h-8 rounded-lg hover:bg-gray-100/80">
                <Menu className="w-4 h-4" />
              </Button>
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-4 h-4 text-white" />
                </div>
                <span className="text-lg font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                  FORMAT123
                </span>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* 主内容区域 - 紧凑 */}
      <main className="relative max-w-7xl mx-auto px-4 lg:px-6 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧用户信息 - 紧凑 */}
          <div className="lg:col-span-1">
            <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-md rounded-xl overflow-hidden">
              <CardContent className="p-0">
                {/* 用户头像区域 - 紧凑 */}
                <div className="relative p-6 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-600 text-white text-center">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-600/20"></div>
                  <Avatar className="relative w-16 h-16 mx-auto mb-3 border-2 border-white/30 shadow-lg">
                    <AvatarImage src={userData.avatar || "/placeholder.svg"} alt={userData.name} />
                    <AvatarFallback className="text-lg bg-gradient-to-br from-blue-600 to-purple-700 text-white">
                      {userData.name[0]}
                    </AvatarFallback>
                  </Avatar>

                  <h2 className="text-lg font-bold mb-1">{userData.name}</h2>
                  <p className="text-blue-100 text-sm mb-1">{userData.email}</p>
                  <p className="text-blue-200 text-xs">加入于 {userData.joinDate}</p>
                </div>

                {/* 统计信息 - 紧凑 */}
                <div className="p-4">
                  <div className="grid grid-cols-3 gap-3 mb-4">
                    <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-lg">
                      <div className="text-lg font-bold text-blue-600 mb-1">{userData.stats.saves}</div>
                      <div className="text-xs text-gray-600 font-medium">收藏</div>
                    </div>
                    <div className="text-center p-3 bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg">
                      <div className="text-lg font-bold text-emerald-600 mb-1">{userData.stats.comments}</div>
                      <div className="text-xs text-gray-600 font-medium">评论</div>
                    </div>
                    <div className="text-center p-3 bg-gradient-to-br from-red-50 to-pink-50 rounded-lg">
                      <div className="text-lg font-bold text-red-600 mb-1">{userData.stats.likes}</div>
                      <div className="text-xs text-gray-600 font-medium">获赞</div>
                    </div>
                  </div>

                  <Button className="w-full h-9 text-sm rounded-lg bg-gradient-to-r from-gray-100 to-gray-50 text-gray-700 hover:from-gray-200 hover:to-gray-100 border border-gray-200/50">
                    <Settings className="w-3 h-3 mr-2" />
                    编辑资料
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 右侧主要内容 - 紧凑 */}
          <div className="lg:col-span-3">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3 bg-white/90 backdrop-blur-sm border-0 shadow-md rounded-lg p-1 h-10">
                <TabsTrigger
                  value="saves"
                  className="flex items-center h-8 text-xs rounded-md data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-md font-medium"
                >
                  <Bookmark className="w-3 h-3 mr-1" />
                  我的收藏
                </TabsTrigger>
                <TabsTrigger
                  value="comments"
                  className="flex items-center h-8 text-xs rounded-md data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-teal-600 data-[state=active]:text-white data-[state=active]:shadow-md font-medium"
                >
                  <MessageCircle className="w-3 h-3 mr-1" />
                  评论记录
                </TabsTrigger>
                <TabsTrigger
                  value="settings"
                  className="flex items-center h-8 text-xs rounded-md data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white data-[state=active]:shadow-md font-medium"
                >
                  <User className="w-3 h-3 mr-1" />
                  账号设置
                </TabsTrigger>
              </TabsList>

              {/* 我的收藏 - 紧凑 */}
              <TabsContent value="saves" className="space-y-6 mt-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-xl font-bold text-gray-900">我的收藏 ({savedResources.length})</h3>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 px-3 text-xs rounded-lg border-gray-200/50 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 bg-transparent"
                    >
                      全部
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 px-3 text-xs rounded-lg hover:bg-gray-100/80">
                      在线工具
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 px-3 text-xs rounded-lg hover:bg-gray-100/80">
                      办公
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                  {savedResources.map((resource) => (
                    <Card
                      key={resource.id}
                      className="group overflow-hidden bg-white/90 backdrop-blur-sm border-0 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1 rounded-xl"
                    >
                      <CardContent className="p-0">
                        <div className="relative">
                          <img
                            src={resource.thumbnail || "/placeholder.svg"}
                            alt={resource.title}
                            className="w-full h-32 object-cover transition-transform duration-300 group-hover:scale-105"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                          <div
                            className={`absolute bottom-2 left-2 px-2 py-1 bg-gradient-to-r ${resource.gradient} text-white rounded-full text-xs font-medium shadow-sm`}
                          >
                            {resource.category}
                          </div>
                        </div>
                        <div className="p-4">
                          <Link href={`/resource/${resource.id}`}>
                            <h4 className="font-semibold text-sm mb-2 text-gray-900 group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-300 line-clamp-1">
                              {resource.title}
                            </h4>
                          </Link>

                          <p className="text-gray-600 text-xs mb-3 line-clamp-2 leading-relaxed">
                            {resource.description}
                          </p>

                          <div className="flex justify-between items-center text-xs text-gray-500">
                            <div className="flex items-center space-x-1">
                              <Clock className="w-3 h-3" />
                              <span>{resource.savedDate}</span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-auto p-0 text-red-500 hover:text-red-600 font-medium text-xs"
                            >
                              取消收藏
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {savedResources.length === 0 && (
                  <div className="text-center py-16">
                    <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
                      <Bookmark className="w-12 h-12 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-bold mb-3 bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent">
                      还没有收藏
                    </h3>
                    <p className="text-gray-500 mb-4">去发现一些有用的资源吧</p>
                    <Button
                      asChild
                      className="px-6 py-2 text-sm rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-md"
                    >
                      <Link href="/">浏览资源</Link>
                    </Button>
                  </div>
                )}
              </TabsContent>

              {/* 评论记录 - 紧凑 */}
              <TabsContent value="comments" className="space-y-6 mt-6">
                <div className="flex justify-between items-center">
                  <h3 className="text-xl font-bold text-gray-900">评论记录 ({userComments.length})</h3>
                </div>

                <div className="space-y-4">
                  {userComments.map((comment) => (
                    <Card key={comment.id} className="bg-white/90 backdrop-blur-sm border-0 shadow-sm rounded-xl">
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start mb-3">
                          <Link
                            href={`/resource/${comment.resourceId}`}
                            className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                          >
                            {comment.resourceTitle}
                          </Link>
                          <span className="text-xs text-gray-500 bg-gray-100/80 px-2 py-1 rounded-full">
                            {comment.date}
                          </span>
                        </div>
                        <p className="text-gray-700 mb-3 leading-relaxed text-sm">{comment.content}</p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-xs text-gray-500">
                            <Heart className="w-3 h-3 mr-1 text-red-500" />
                            <span className="font-medium">{comment.likes} 人点赞</span>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 px-3 text-xs rounded-lg border-gray-200/50 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 bg-transparent"
                            asChild
                          >
                            <Link href={`/resource/${comment.resourceId}`}>查看详情</Link>
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {userComments.length === 0 && (
                  <div className="text-center py-16">
                    <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-full flex items-center justify-center">
                      <MessageCircle className="w-12 h-12 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-bold mb-3 bg-gradient-to-r from-gray-600 to-gray-800 bg-clip-text text-transparent">
                      还没有评论
                    </h3>
                    <p className="text-gray-500">参与讨论，分享你的想法</p>
                  </div>
                )}
              </TabsContent>

              {/* 账号设置 - 紧凑 */}
              <TabsContent value="settings" className="space-y-6 mt-6">
                <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-md rounded-xl">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg font-bold text-gray-900">基本信息</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {!isEditing ? (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-lg">
                            <Label className="text-xs font-semibold text-gray-600 mb-1 block">用户名</Label>
                            <p className="font-medium text-gray-900">{userData.name}</p>
                          </div>
                          <div className="p-4 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg">
                            <Label className="text-xs font-semibold text-gray-600 mb-1 block">邮箱</Label>
                            <p className="font-medium text-gray-900">{userData.email}</p>
                          </div>
                        </div>
                        <Button
                          onClick={() => setIsEditing(true)}
                          className="px-6 py-2 text-sm rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-md"
                        >
                          编辑信息
                        </Button>
                      </>
                    ) : (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="name" className="text-xs font-semibold text-gray-700 mb-1 block">
                              用户名
                            </Label>
                            <Input
                              id="name"
                              value={editForm.name}
                              onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                              className="h-9 bg-white/80 border-gray-200/50 rounded-lg focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50"
                            />
                          </div>
                          <div>
                            <Label htmlFor="email" className="text-xs font-semibold text-gray-700 mb-1 block">
                              邮箱
                            </Label>
                            <Input
                              id="email"
                              type="email"
                              value={editForm.email}
                              onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                              className="h-9 bg-white/80 border-gray-200/50 rounded-lg focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50"
                            />
                          </div>
                        </div>
                        <div className="flex space-x-3">
                          <Button
                            onClick={handleSaveProfile}
                            className="px-6 py-2 text-sm rounded-lg bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white shadow-md"
                          >
                            保存
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setIsEditing(false)}
                            className="px-6 py-2 text-sm rounded-lg border-gray-200/50 hover:bg-gray-50/80"
                          >
                            取消
                          </Button>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>

                <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-md rounded-xl">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg font-bold text-gray-900">偏好设置</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-gradient-to-br from-blue-50 to-cyan-50 rounded-lg">
                      <div>
                        <Label className="font-semibold text-gray-900 mb-1 block">邮件通知</Label>
                        <p className="text-xs text-gray-600">接收新评论和点赞通知</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 px-4 text-xs rounded-lg border-gray-200/50 hover:bg-emerald-50 hover:text-emerald-600 hover:border-emerald-200 bg-transparent"
                      >
                        开启
                      </Button>
                    </div>
                    <div className="flex items-center justify-between p-4 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg">
                      <div>
                        <Label className="font-semibold text-gray-900 mb-1 block">隐私设置</Label>
                        <p className="text-xs text-gray-600">设置个人资料的可见性</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 px-4 text-xs rounded-lg border-gray-200/50 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 bg-transparent"
                      >
                        公开
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-md rounded-xl">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg font-bold text-red-600">危险操作</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="p-4 bg-gradient-to-r from-red-50 to-pink-50 rounded-lg">
                      <Label className="font-semibold text-gray-900 mb-2 block">删除账号</Label>
                      <p className="text-xs text-gray-600 mb-3">删除账号后，所有数据将无法恢复</p>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="h-8 px-4 text-xs rounded-lg bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 shadow-md"
                      >
                        删除账号
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </div>
  )
}
