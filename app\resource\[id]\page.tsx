"use client"

import { useState } from "react"
import {
  ArrowLeft,
  Heart,
  ThumbsDown,
  MessageCircle,
  Star,
  ExternalLink,
  Share2,
  Flag,
  Zap,
  TrendingUp,
  Menu,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { useRouter } from "next/navigation"

const resourceData = {
  id: 1,
  title: "PDF转换大师",
  description:
    "这是一款功能强大的在线PDF转换工具，支持PDF与Word、Excel、PowerPoint、图片等多种格式之间的相互转换。界面简洁易用，转换速度快，支持批量处理，是办公和学习的必备工具。无需安装任何软件，完全基于云端处理，保护您的隐私安全。",
  category: "在线工具",
  thumbnail: "/pdf-converter-interface.png",
  url: "https://example.com/pdf-converter",
  likes: 1234,
  dislikes: 12,
  comments: 89,
  saves: 456,
  rating: 4.8,
  tags: ["PDF转换", "办公工具", "文档处理", "在线工具", "批量处理"],
  features: [
    "支持20+种格式相互转换",
    "批量处理，提升工作效率",
    "高质量转换，保持原始格式",
    "无需安装软件，在线即用",
    "云端处理，保护隐私安全",
    "支持大文件上传处理",
  ],
}

const mockComments = [
  {
    id: 1,
    user: "张三",
    avatar: "/user-avatar-1.png",
    content: "这个工具真的很好用，转换速度很快，质量也不错！界面设计也很简洁，用起来很舒服。",
    time: "2小时前",
    likes: 12,
  },
  {
    id: 2,
    user: "李四",
    avatar: "/diverse-user-avatar-set-2.png",
    content: "界面简洁，操作简单，推荐给大家使用。特别是批量转换功能很实用。",
    time: "5小时前",
    likes: 8,
  },
  {
    id: 3,
    user: "王五",
    avatar: "/diverse-user-avatars-3.png",
    content: "免费版功能就很够用了，比其他同类工具好用多了。转换质量很高。",
    time: "1天前",
    likes: 15,
  },
]

export default function ResourceDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [showAllComments, setShowAllComments] = useState(false)
  const [newComment, setNewComment] = useState("")
  const [isLiked, setIsLiked] = useState(false)
  const [isDisliked, setIsDisliked] = useState(false)
  const [isSaved, setIsSaved] = useState(false)

  const handleInteraction = (action: string) => {
    if (!isLoggedIn) {
      alert("登录后可操作")
      return
    }

    switch (action) {
      case "like":
        setIsLiked(!isLiked)
        setIsDisliked(false)
        break
      case "dislike":
        setIsDisliked(!isDisliked)
        setIsLiked(false)
        break
      case "save":
        setIsSaved(!isSaved)
        break
    }
  }

  const handleCommentSubmit = () => {
    if (!isLoggedIn) {
      alert("登录后可评论")
      return
    }
    if (newComment.trim()) {
      console.log("提交评论:", newComment)
      setNewComment("")
    }
  }

  const displayedComments = showAllComments ? mockComments : mockComments.slice(0, 3)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 科技感背景装饰 - 缩小 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-32 -right-32 w-64 h-64 bg-gradient-to-br from-blue-400/15 to-purple-600/15 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-32 -left-32 w-64 h-64 bg-gradient-to-tr from-cyan-400/15 to-blue-600/15 rounded-full blur-3xl"></div>
      </div>

      {/* 导航栏 - 紧凑 */}
      <header className="relative bg-white/90 backdrop-blur-xl border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 lg:px-6">
          <div className="flex justify-between items-center h-14">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="h-8 px-3 rounded-lg hover:bg-gray-100/80"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                返回
              </Button>
              {/* 移动端菜单按钮 */}
              <Button variant="ghost" className="lg:hidden w-8 h-8 rounded-lg hover:bg-gray-100/80">
                <Menu className="w-4 h-4" />
              </Button>
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Zap className="w-4 h-4 text-white" />
                </div>
                <span className="text-lg font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                  FORMAT123
                </span>
              </Link>
            </div>

            {!isLoggedIn ? (
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setIsLoggedIn(true)}
                  className="h-8 px-4 text-xs rounded-lg border-gray-200/50 hover:bg-gray-50/80"
                >
                  登录
                </Button>
                <Button
                  onClick={() => setIsLoggedIn(true)}
                  className="h-8 px-4 text-xs rounded-lg bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-md"
                >
                  注册
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <span className="text-xs text-gray-600">欢迎回来！</span>
                <Button
                  variant="outline"
                  onClick={() => setIsLoggedIn(false)}
                  className="h-8 px-4 text-xs rounded-lg border-gray-200/50 hover:bg-gray-50/80"
                >
                  退出
                </Button>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* 主内容区域 - 紧凑 */}
      <main className="relative max-w-7xl mx-auto px-4 lg:px-6 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 左侧主要内容 */}
          <div className="lg:col-span-3 space-y-6">
            {/* 资源信息卡片 - 紧凑 */}
            <Card className="overflow-hidden bg-white/90 backdrop-blur-sm border-0 shadow-md rounded-xl">
              <CardContent className="p-0">
                <div className="relative">
                  {/* 大图预览 - 紧凑 */}
                  <div className="relative h-64 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-cyan-50 to-blue-50 opacity-50"></div>
                    <img
                      src={resourceData.thumbnail || "/placeholder.svg"}
                      alt={resourceData.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>

                    {/* 浮动信息 - 紧凑 */}
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center space-x-1 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                            <Star className="w-3 h-3 text-yellow-500 fill-current" />
                            <span className="font-semibold text-gray-800 text-sm">{resourceData.rating}</span>
                          </div>
                          <div className="flex items-center space-x-1 bg-gradient-to-r from-red-500 to-orange-500 text-white px-3 py-1 rounded-full">
                            <TrendingUp className="w-3 h-3" />
                            <span className="font-medium text-xs">HOT</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1 bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-3 py-1 rounded-full">
                          <Zap className="w-3 h-3" />
                          <span className="font-medium text-xs">{resourceData.category}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 内容信息 - 紧凑 */}
                  <div className="p-6">
                    <div className="mb-4">
                      <h1 className="text-2xl font-bold mb-2 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                        {resourceData.title}
                      </h1>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                        <div className="flex items-center space-x-1">
                          <Heart className="w-3 h-3" />
                          <span className="font-medium">{resourceData.likes}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageCircle className="w-3 h-3" />
                          <span className="font-medium">{resourceData.comments}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Star className="w-3 h-3" />
                          <span className="font-medium">{resourceData.saves}</span>
                        </div>
                      </div>
                    </div>

                    <p className="text-gray-700 leading-relaxed mb-6">{resourceData.description}</p>

                    {/* 功能特点 - 紧凑 */}
                    <div className="mb-6">
                      <h3 className="text-lg font-bold mb-3 text-gray-900">功能特点</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {resourceData.features.map((feature, index) => (
                          <div
                            key={index}
                            className="flex items-center space-x-2 p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg"
                          >
                            <div className="w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                            <span className="text-gray-700 text-sm font-medium">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* 标签 - 紧凑 */}
                    <div className="flex flex-wrap gap-2">
                      {resourceData.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-gradient-to-r from-gray-100 to-gray-50 text-gray-700 rounded-lg font-medium text-sm border border-gray-200/50"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 评论区域 - 紧凑 */}
            <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-md rounded-xl">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center text-lg font-bold text-gray-900">
                  <MessageCircle className="w-5 h-5 mr-2 text-blue-500" />
                  评论讨论 ({mockComments.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 评论输入框 - 紧凑 */}
                {isLoggedIn ? (
                  <div className="space-y-3">
                    <Textarea
                      placeholder="分享你的使用体验..."
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      className="min-h-[80px] bg-white/80 border-gray-200/50 rounded-lg resize-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50"
                    />
                    <div className="flex justify-end">
                      <Button
                        onClick={handleCommentSubmit}
                        disabled={!newComment.trim()}
                        className="px-6 py-2 text-sm rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-md"
                      >
                        发布评论
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 text-center">
                    <MessageCircle className="w-8 h-8 mx-auto mb-3 text-gray-400" />
                    <p className="text-gray-600 mb-3">登录后参与讨论</p>
                    <Button
                      onClick={() => setIsLoggedIn(true)}
                      className="px-6 py-2 text-sm rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-md"
                    >
                      立即登录
                    </Button>
                  </div>
                )}

                <Separator className="bg-gray-200/50" />

                {/* 评论列表 - 紧凑 */}
                <div className="space-y-4">
                  {displayedComments.map((comment) => (
                    <div
                      key={comment.id}
                      className="flex space-x-3 p-4 bg-gradient-to-r from-gray-50/50 to-blue-50/30 rounded-lg"
                    >
                      <Avatar className="w-8 h-8 border border-white shadow-sm">
                        <AvatarImage src={comment.avatar || "/placeholder.svg"} alt={comment.user} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white text-xs font-semibold">
                          {comment.user[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-semibold text-gray-900 text-sm">{comment.user}</span>
                          <span className="text-xs text-gray-500">{comment.time}</span>
                        </div>
                        <p className="text-gray-700 mb-2 leading-relaxed text-sm">{comment.content}</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 px-2 rounded-lg hover:bg-red-50 hover:text-red-500 transition-colors"
                        >
                          <Heart className="w-3 h-3 mr-1" />
                          <span className="text-xs font-medium">{comment.likes}</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {mockComments.length > 3 && (
                  <div className="text-center pt-3">
                    <Button
                      variant="outline"
                      onClick={() => setShowAllComments(!showAllComments)}
                      className="px-6 py-2 text-sm rounded-lg border-gray-200/50 hover:bg-gray-50/80"
                    >
                      {showAllComments ? "收起评论" : `查看全部 ${mockComments.length} 条评论`}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 右侧操作栏 - 紧凑 */}
          <div className="space-y-4">
            {/* 主要操作 - 紧凑 */}
            <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-md rounded-xl">
              <CardContent className="p-4 space-y-4">
                <Button
                  className="w-full h-10 text-sm font-semibold bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-lg shadow-md transition-all duration-300"
                  asChild
                >
                  <a href={resourceData.url} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    立即体验
                  </a>
                </Button>

                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handleInteraction("like")}
                    className={`h-9 rounded-lg border-gray-200/50 transition-all duration-300 ${
                      !isLoggedIn ? "opacity-50" : ""
                    } ${
                      isLiked
                        ? "bg-gradient-to-r from-red-500 to-pink-500 text-white border-transparent shadow-md"
                        : "hover:bg-red-50 hover:text-red-500 hover:border-red-200"
                    }`}
                  >
                    <Heart className={`w-4 h-4 ${isLiked ? "fill-current" : ""}`} />
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleInteraction("dislike")}
                    className={`h-9 rounded-lg border-gray-200/50 transition-all duration-300 ${
                      !isLoggedIn ? "opacity-50" : ""
                    } ${
                      isDisliked
                        ? "bg-gradient-to-r from-gray-500 to-gray-600 text-white border-transparent shadow-md"
                        : "hover:bg-gray-50 hover:text-gray-600 hover:border-gray-300"
                    }`}
                  >
                    <ThumbsDown className={`w-4 h-4 ${isDisliked ? "fill-current" : ""}`} />
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleInteraction("save")}
                    className={`h-9 rounded-lg border-gray-200/50 transition-all duration-300 ${
                      !isLoggedIn ? "opacity-50" : ""
                    } ${
                      isSaved
                        ? "bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-transparent shadow-md"
                        : "hover:bg-yellow-50 hover:text-yellow-600 hover:border-yellow-200"
                    }`}
                  >
                    <Star className={`w-4 h-4 ${isSaved ? "fill-current" : ""}`} />
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    className="h-9 text-xs rounded-lg border-gray-200/50 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 bg-transparent"
                  >
                    <Share2 className="w-3 h-3 mr-1" />
                    分享
                  </Button>
                  <Button
                    variant="outline"
                    className="h-9 text-xs rounded-lg border-gray-200/50 hover:bg-red-50 hover:text-red-600 hover:border-red-200 bg-transparent"
                  >
                    <Flag className="w-3 h-3 mr-1" />
                    举报
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 统计信息 - 紧凑 */}
            <Card className="bg-white/90 backdrop-blur-sm border-0 shadow-md rounded-xl">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-bold text-gray-900">数据统计</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-gradient-to-r from-red-50 to-pink-50 rounded-lg">
                  <span className="text-gray-700 font-medium text-sm">点赞数</span>
                  <span className="text-lg font-bold text-red-600">{resourceData.likes}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg">
                  <span className="text-gray-700 font-medium text-sm">收藏数</span>
                  <span className="text-lg font-bold text-orange-600">{resourceData.saves}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg">
                  <span className="text-gray-700 font-medium text-sm">评论数</span>
                  <span className="text-lg font-bold text-blue-600">{resourceData.comments}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg">
                  <span className="text-gray-700 font-medium text-sm">综合评分</span>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-lg font-bold text-purple-600">{resourceData.rating}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
